import { getResources, getFilters } from "@/features/resources/services";
import { FilterParams } from "@/features/resources/types";
import { useQuery } from "@tanstack/react-query";

export const useGetResources = (
	subject: string,
	resourceType: string,
	searchTerms: string[] | string,
	filters?: FilterParams
) =>
	useQuery({
		queryKey: ["resources", subject, resourceType, searchTerms, filters],
		queryFn: () => {
			// Convert searchTerms array to a space-separated string if it's an array
			const searchString = Array.isArray(searchTerms)
				? searchTerms.join(" ")
				: searchTerms;

			return getResources(subject, resourceType, searchString, filters);
		},
		enabled: !!subject && !!resourceType,
		select: (data) => data.data,
	});

export const useGetFilters = (
	subject: string,
	resourceType: string,
	filters?: FilterParams
) =>
	useQuery({
		queryKey: ["filters", subject, resourceType, filters],
		queryFn: () => getFilters(subject, resourceType, filters),
		enabled: !!subject && !!resourceType,
		select: (response) => response.data.data,
		staleTime: 5 * 60 * 1000, // 5 minutes - filters don't change often
	});
