import { Input } from "@/components/ui/input";
import useMediaQuery from "@/hooks/use-media-query";
import { Search } from "react-feather";
import clsx from "clsx";

interface SearchParhlaiProps {
	visibleInMobile?: boolean;
	disabled?: boolean;
}

const SearchParhlai: React.FC<SearchParhlaiProps> = ({
	visibleInMobile = true,
	disabled = false,
}) => {
	const isDesktop = useMediaQuery("(min-width: 1024px)");
	if (isDesktop)
		return (
			<div className="flex max-w-96 flex-1 gap-x-3">
				<Input
					startIcon={Search}
					placeholder="Search Here"
					disabled={disabled}
					className={clsx(
						"sans h-14 rounded-[10px] items-center",
						"placeholder:text-gray-400 text-gray-600",
						{
							"opacity-50 cursor-not-allowed": disabled,
						}
					)}
				/>
			</div>
		);
	else
		return (
			<div
				className={clsx(visibleInMobile ? "lg:hidden" : "invisible", {
					"opacity-50 cursor-not-allowed": disabled,
				})}
			>
				<Search size={24} />
			</div>
		);
};

export default SearchParhlai;
