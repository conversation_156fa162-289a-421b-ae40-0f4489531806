import React from "react";
import MobileSwitcher from "@/components/layout/main/mobile-switcher";
import { Navbar } from "@/components/layout/main/navbar";
import useMediaQuery from "@/hooks/use-media-query";
import { createFileRoute, Link as RouterLink } from "@tanstack/react-router";
import { useAuthStore } from "@/features/auth/store";
import { useShallow } from "zustand/react/shallow";
import {
	AnalyticsCard,
	LoadingCard,
	AnalyticsEmptyState,
} from "@/components/common";
import { useGetUser, useGetAnalytics } from "@/lib/queries/user.query";
import {
	Link,
	Bookmark,
	FileText,
	Edit,
	FileMinus,
	Clock,
} from "react-feather";
import { Button } from "@/components/ui/button";

// Quick Action Card Interface
interface QuickActionCardProps {
	title: string;
	description: string;
	icon: React.ComponentType<any>;
	href?: string;
	disabled?: boolean;
}

// Quick Action Card Component
const QuickActionCard: React.FC<QuickActionCardProps> = ({
	title,
	description,
	icon: Icon,
	href,
	disabled = false,
}) => {
	const cardContent = (
		<div className="flex-shrink-0 w-[100px] md:w-auto">
			{/* Mobile layout - icon only */}
			<div
				className={
					"md:hidden flex flex-col items-center p-4 rounded-[14px] bg-[#5936CD1F] border border-purple-200 " +
					(disabled
						? "opacity-50 cursor-not-allowed"
						: "cursor-pointer hover:shadow-md transition-shadow")
				}
			>
				<div className="p-2 rounded-full bg-white shadow-sm mb-2">
					<Icon size={20} className="text-accent" />
				</div>
				<h3 className="font-semibold text-gray-600 text-sm text-center">
					{title}
				</h3>
			</div>

			{/* Desktop layout - original design */}
			<div
				className={
					"hidden md:block py-2.5 px-4 rounded-[14px] border bg-[#5936CD1F] border-purple-200 " +
					(disabled
						? "opacity-50 cursor-not-allowed hover:shadow-none"
						: "cursor-pointer hover:shadow-md transition-shadow")
				}
			>
				<div className="flex items-center gap-3">
					<div className="p-2 rounded-xl bg-white shadow-sm">
						<Icon size={20} className="text-accent" />
					</div>
					<div className="flex-1">
						<h3 className="font-semibold text-gray-600 text-base mb-0.5">
							{title}
						</h3>
						<p className="text-gray-500 text-xs">{description}</p>
					</div>
				</div>
			</div>
		</div>
	);

	// If href is provided, wrap in RouterLink, otherwise return plain content
	if (href) {
		return <RouterLink to={href}>{cardContent}</RouterLink>;
	}

	return cardContent;
};

const Page = () => {
	const isDesktop = useMediaQuery("(min-width: 1024px)");
	const { user } = useAuthStore(
		useShallow((state) => ({
			user: state.user,
		}))
	);

	// Fetch user details from API using firebase ID
	const { data: userData } = useGetUser(user?.uid || "");
	const apiUser = userData?.data?.data;

	// Fetch analytics data from API
	const {
		data: analyticsData,
		isLoading: analyticsLoading,
		error: analyticsError,
	} = useGetAnalytics();
	const analytics = analyticsData?.data?.data?.analytics;

	// Extract user details - prioritize database name if it exists and is not empty
	const displayName =
		apiUser?.name && apiUser.name.trim() !== ""
			? apiUser.name
			: user?.displayName || user?.email?.split("@")[0] || "User";

	// Quick Actions Data
	const quickActions = [
		{
			title: "Resources",
			description: "Videos, links & more",
			icon: Link,
			href: "/learn",
		},
		{
			title: "Test",
			description: "Create mock test",
			icon: Edit,
			href: "/t/mock",
		},
		{
			title: "Saved",
			description: "Nothing saved yet",
			icon: Bookmark,
			disabled: true,
		},
		{
			title: "Attempts",
			description: "See attempted tests",
			icon: FileText,
			disabled: true,
		},
		// {
		// 	title: "FAQ's",
		// 	description: "frequently asked q..",
		// 	icon: HelpCircle,
		// },
	];

	return (
		<>
			{!isDesktop && (
				<>
					<Navbar />
					<MobileSwitcher />
				</>
			)}
			<main className="container mx-auto p-4 pb-20 lg:pb-4">
				{/* Header */}
				<div className="mb-10 lg:mb-5">
					<h2 className="mb-2 font-bold text-[28px] lg:text-[32px] text-[#211C37]">
						Hello {displayName} 👋🏼
					</h2>
					<p className="text-gray-500 text-sm lg:text-base">
						Here for more knowledge? Let's power through your preparation!
					</p>
				</div>

				{/* Quick Actions */}
				<div className="mb-[30px] p-0 lg:mb-5 lg:p-6 lg:bg-white lg:rounded-2xl lg:shadow-[6px_6px_54px_0px_#0000000D]">
					<div className="flex justify-between items-center mb-4 lg:mb-6">
						<h2 className="font-bold text-lg lg:text-2xl text-[#202224]">
							Quick Actions
						</h2>
					</div>
					{/* Mobile: Horizontal scroll layout */}
					<div className="md:hidden flex gap-2.5 overflow-x-auto pb-2 scrollbar-hide">
						{quickActions.map((action, index) => (
							<QuickActionCard
								key={index}
								title={action.title}
								description={action.description}
								icon={action.icon}
								href={action.href}
								disabled={action.disabled}
							/>
						))}
					</div>

					{/* Desktop: Grid layout */}
					<div className="hidden md:grid md:grid-cols-4 gap-4">
						{quickActions.map((action, index) => (
							<QuickActionCard
								key={index}
								title={action.title}
								description={action.description}
								icon={action.icon}
								href={action.href}
								disabled={action.disabled}
							/>
						))}
					</div>
				</div>

				{/* Analytics */}
				<div className="mb-[30px] p-0 lg:mb-5 lg:p-6 lg:bg-white lg:rounded-2xl lg:shadow-[6px_6px_54px_0px_#0000000D]">
					<div className="flex justify-between items-center mb-4 lg:mb-6">
						<h2 className="font-bold text-lg lg:text-2xl text-[#202224]">
							Analytics
						</h2>
						<Button variant="outline" className="rounded-lg" asChild>
							<RouterLink to="/analytics">See All Analytics</RouterLink>
						</Button>
					</div>

					{analyticsLoading ? (
						<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
							{[...Array(3)].map((_, i) => (
								<LoadingCard key={i} variant="analytics" />
							))}
						</div>
					) : analyticsError ? (
						<AnalyticsEmptyState variant="no-data" noBg noBorder noMb />
					) : !analytics?.totalQuizzesTaken ||
					  analytics.totalQuizzesTaken === 0 ? (
						<AnalyticsEmptyState
							variant="no-tests-completed"
							noBg
							noBorder
							noMb
						/>
					) : (
						<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
							<AnalyticsCard
								heading="MCQs Solved"
								number={analytics?.mcqsSolvedCount || 0}
								icon={FileText}
								helperText="Down from yesterday"
								colorVariant="blue"
								trendDirection="down"
								trendPercentage="-"
							/>
							<AnalyticsCard
								heading="Avg Score Per Quiz"
								number={analytics?.avgScorePerQuiz || 0}
								icon={FileMinus}
								helperText="Up from past week"
								colorVariant="red"
								trendDirection="up"
								trendPercentage="-"
							/>
							<AnalyticsCard
								heading="Avg Time Per Quiz"
								number={analytics?.avgTimePerQuiz || 0}
								icon={Clock}
								helperText="Up from yesterday"
								colorVariant="green"
								trendDirection="up"
								trendPercentage="-"
							/>
						</div>
					)}
				</div>

				{/* Commented Resource Card */}
				{/* <ResourceCard
					title="Boost Your Prep with Expert Resources!"
					description="Access videos, documents, and links curated to help you master every topic."
					buttonText="Explore Resources"
					buttonLink="/learn"
				/> */}
			</main>
		</>
	);
};

export const Route = createFileRoute("/(app)/_mainlayout/dashboard")({
	beforeLoad: () => {},
	component: Page,
});
