import { createFileRoute } from "@tanstack/react-router";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { ResourceTabs } from "@/components/layout/learning/resource-tabs";
import { subjects } from "@/features/learning/constants";
import EducationFilter from "@/components/layout/learning/education-filter";
import MobileHeader from "@/components/common/mobile-header";
import useMediaQuery from "@/hooks/use-media-query";
import MobileLearningView from "@/components/layout/learning/mobile";
import { useState } from "react";
import { useQuery, useQueries } from "@tanstack/react-query";
import { getResources } from "@/features/resources/services";
import { FilterParams } from "@/features/resources/types";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

const Page = () => {
	const [currentResourceType, setCurrentResourceType] =
		useState<string>("video");
	const isDesktop = useMediaQuery("(min-width: 1024px)");
	const [selectedSubject, setSelectedSubject] = useState<string>("Physics");
	const [searchTerm, setSearchTerm] = useState<string>("");
	const [searchInput, setSearchInput] = useState<string>("");
	const [filters, setFilters] = useState<FilterParams>({
		entryTests: [],
		chapters: [],
		topics: [],
		language: [],
		categories: [],
	});

	const handleFiltersChange = (newFilters: FilterParams) => {
		console.log(newFilters, "__________newFilters");
		setFilters(newFilters);
	};

	console.log(filters, "__________filters");

	const handleResourceTypeChange = (type: string) => {
		setCurrentResourceType(type);
		// Clear filters when resource type changes since different types may have different available filters
		setFilters({
			entryTests: [],
			chapters: [],
			topics: [],
			language: [],
			categories: [],
		});
	};

	// For desktop: Only call API for the current active resource type
	// For mobile: Call APIs for all resource types (since mobile shows all at once)
	const { data: currentResourceData, isLoading: isCurrentResourceLoading } =
		useQuery({
			queryKey: [
				"resources",
				{
					type: currentResourceType,
					selectedSubject,
					searchTerm,
					filters,
				},
			],
			queryFn: () =>
				getResources(selectedSubject, currentResourceType, searchTerm, filters),
			enabled: !!selectedSubject && !!currentResourceType && isDesktop,
			retry: 0,
		});

	// For mobile: fetch all resource types
	const allResourcesQueries = useQueries({
		queries: ["video", "file", "link"].map((type) => ({
			queryKey: [
				"resources",
				{
					type,
					selectedSubject,
					searchTerm,
					filters,
				},
			],
			queryFn: () => getResources(selectedSubject, type, searchTerm, filters),
			enabled: !!selectedSubject && !isDesktop,
			retry: 0,
		})),
	});

	// Prepare resources based on device type
	const resources = isDesktop
		? [currentResourceData?.data.data || []]
		: allResourcesQueries.map((result) => result.data?.data.data);

	const isResourcesLoading = isDesktop
		? isCurrentResourceLoading
		: allResourcesQueries.some((result) => result.isLoading);

	const handleSearch = () => {
		setSearchTerm(searchInput);
	};

	return (
		<>
			{isDesktop ? (
				<div className="pt-6">
					<h3 className="font-bold hidden lg:block mb-6 px-6">
						Learning Suggestions
					</h3>
					<Tabs
						onValueChange={(value) => setSelectedSubject(value)}
						defaultValue={selectedSubject ?? subjects[0]?.id}
						className="w-full"
					>
						<TabsList className="flex">
							{subjects.map((subject) => (
								<TabsTrigger
									key={subject.id}
									value={subject.id}
									className="rounded-t-[10px] px-6 font-medium text-[13px] data-[state=active]:text-gray-700"
								>
									{subject.label}
								</TabsTrigger>
							))}
						</TabsList>

						<div className="bg-white rounded-lg p-4 min-h-screen">
							{/* Search input for resources */}
							<div className="mb-4 flex gap-2">
								<Input
									placeholder="Search resources..."
									value={searchInput}
									onChange={(e) => setSearchInput(e.target.value)}
									onKeyDown={(e) => e.key === "Enter" && handleSearch()}
									className="max-w-md"
								/>
								<Button onClick={handleSearch} variant="outline">
									Search
								</Button>
							</div>

							<div className="grid lg:grid-cols-3 gap-2">
								<div className="lg:col-span-2">
									{subjects.map((subject) => (
										<TabsContent
											key={subject.id}
											value={subject.id}
											className="mt-0"
										>
											<ResourceTabs
												isLoading={isResourcesLoading}
												resources={resources}
												currentResourceType={currentResourceType}
												onResourceTypeChange={handleResourceTypeChange}
											/>
										</TabsContent>
									))}
								</div>
								<div className="lg:col-span-1">
									<h3 className="text-sm font-medium mb-2">Filters</h3>
									<EducationFilter
										subject={selectedSubject}
										resourceType={currentResourceType}
										onFiltersChange={handleFiltersChange}
									/>
								</div>
							</div>
						</div>
					</Tabs>
				</div>
			) : (
				<>
					<MobileHeader resourceType={currentResourceType} />
					<div className="pt-[80px]">
						{/* Mobile search input */}
						<div className="p-4 flex gap-2">
							<Input
								placeholder="Search resources..."
								value={searchInput}
								onChange={(e) => setSearchInput(e.target.value)}
								onKeyDown={(e) => e.key === "Enter" && handleSearch()}
							/>
							<Button onClick={handleSearch} variant="outline">
								Search
							</Button>
						</div>
						<MobileLearningView
							resources={resources}
							setResourceType={setCurrentResourceType}
							selectedSubject={selectedSubject}
							setSelectedSubject={setSelectedSubject}
						/>
					</div>
				</>
			)}
		</>
	);
};

export const Route = createFileRoute("/(app)/_mainlayout/learn")({
	component: Page,
});

export default Page;
