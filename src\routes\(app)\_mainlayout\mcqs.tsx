import { createFile<PERSON>out<PERSON>, useNavigate } from "@tanstack/react-router";
import { useState, useEffect } from "react";
import useMediaQuery from "@/hooks/use-media-query";
import TestLayout from "@/components/layout/mcqs/test-layout";
import MCQMobileLayout from "@/components/layout/mcqs/mcq-mobile-layout";
import { Test, MCQ } from "@/features/mcqs/types";
import { MockTestResponse, MockTestMCQ } from "@/features/tests/services";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { BookOpen } from "react-feather";

// Convert API format to the Test format used by the components
const convertApiDataToTest = (apiData: MockTestResponse): Test => {
	const mcqs: MCQ[] = [];

	// Flatten the MCQs by subject into a single array
	Object.entries(apiData.mcqsBySubject).forEach(([subject, mcqList]) => {
		mcqList.forEach((apiMcq: MockTestMCQ) => {
			// Ensure the subject is a valid one from our constants
			const validSubject = subject as MCQ["subject"];

			mcqs.push({
				id: apiMcq._id,
				question: apiMcq.title,
				options: apiMcq.options,
				correctAnswer: apiMcq.answer,
				subject: validSubject,
				tag: apiMcq.type,
				difficulty: apiMcq.difficulty,
				description: apiMcq.explanation,
			});
		});
	});

	// Format duration based on minutes and seconds from the API response
	// We'll only use totalSeconds since it's more accurate for time calculation
	const totalSeconds = apiData.time.seconds || 0;

	// The API provides both minutes and total seconds, but we'll use totalSeconds for accuracy
	const hours = Math.floor(totalSeconds / 3600);
	const remainingMinutes = Math.floor((totalSeconds % 3600) / 60);
	const remainingSeconds = totalSeconds % 60;

	// Format as HH:MM:SS
	const formattedDuration = `${hours.toString().padStart(2, "0")}:${remainingMinutes
		.toString()
		.padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;

	return {
		id: apiData.quizId, // Use the actual quizId from the API response
		title: `${apiData.requestedParams.entryTest} Mock Test`,
		totalQuestions: apiData.count,
		duration: formattedDuration,
		liveCheck: false,
		mcqs: mcqs,
	};
};

// Static component to show when no test data is available
const MCQStaticComponent = () => {
	const navigate = useNavigate();

	const handleGoToMockTest = () => {
		navigate({ to: "/t/mock" });
	};

	return (
		<div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 p-4">
			<div className="bg-white rounded-3xl border border-gray-300 p-8 max-w-md w-full text-center">
				<div className="w-16 h-16 bg-[rgba(89,54,205,0.1)] rounded-full flex items-center justify-center mb-6 mx-auto">
					<BookOpen className="w-8 h-8 text-accent" />
				</div>
				<h2 className="text-xl font-semibold text-gray-700 mb-3">
					No Active Test
				</h2>
				<p className="text-sm text-gray-500 mb-6 leading-relaxed">
					You don't have any active test session. Start a mock test to begin
					practicing with MCQs.
				</p>
				<Button
					onClick={handleGoToMockTest}
					className="bg-accent hover:bg-[#4A2BA8] text-white px-6 py-3 rounded-lg font-medium transition-colors w-full"
				>
					Go to Mock Test
				</Button>
			</div>
		</div>
	);
};

const Page = () => {
	const isDesktop = useMediaQuery("(min-width: 1024px)");
	const [test, setTest] = useState<Test | null>(null);
	const [liveCheckEnabled, setLiveCheckEnabled] = useState(false);
	const [selectedAnswers, setSelectedAnswers] = useState<
		Record<string, number>
	>({});
	const [answerStatus, setAnswerStatus] = useState<Record<string, boolean>>({});
	const [showResults, setShowResults] = useState(false);
	const [testTimesUp, setTestTimesUp] = useState(false);
	// const [testStarted, setTestStarted] = useState(false);
	const { toast } = useToast();

	// Get test data from localStorage and set up reload warning
	useEffect(() => {
		const storedTestData = localStorage.getItem("currentTestData");

		if (storedTestData) {
			try {
				const apiTestData = JSON.parse(storedTestData) as MockTestResponse;
				const convertedTest = convertApiDataToTest(apiTestData);
				setTest(convertedTest);
				// setTestStarted(true);
				setLiveCheckEnabled(false); // Reset live check for new test
				setSelectedAnswers({}); // Reset answers for new test
				setAnswerStatus({}); // Reset status for new test
				setShowResults(false); // Reset results for new test

				// Remove the localStorage data immediately after loading
				localStorage.removeItem("currentTestData");
			} catch (error) {
				console.error("Failed to parse test data:", error);
				toast({
					title: "Error",
					description: "Failed to load test data.",
					variant: "destructive",
				});
				setTest(null);
			}
		} else {
			setTest(null);
		}
	}, [toast]);

	// // Add beforeunload event listener for reload warning
	// useEffect(() => {
	// 	const handleBeforeUnload = (event: BeforeUnloadEvent) => {
	// 		// Only show warning if test is active and not finished
	// 		if (test && testStarted && !testTimesUp && !showResults) {
	// 			const message =
	// 				"You will lose your test progress if you reload. Are you sure you want to continue?";
	// 			event.preventDefault();
	// 			// eslint-disable-next-line deprecation/deprecation
	// 			event.returnValue = message;
	// 			return message;
	// 		}
	// 		return undefined;
	// 	};

	// 	window.addEventListener("beforeunload", handleBeforeUnload);

	// 	return () => {
	// 		window.removeEventListener("beforeunload", handleBeforeUnload);
	// 	};
	// }, [test, testStarted, testTimesUp, showResults]);

	// // Add navigation blocking for route changes
	// useEffect(() => {
	// 	let isNavigationBlocked = false;

	// 	const handlePopState = () => {
	// 		// Only show warning if test is active and not finished
	// 		if (
	// 			test &&
	// 			testStarted &&
	// 			!testTimesUp &&
	// 			!showResults &&
	// 			!isNavigationBlocked
	// 		) {
	// 			isNavigationBlocked = true;
	// 			const shouldLeave = window.confirm(
	// 				"You will lose your test progress if you leave this page. Are you sure you want to continue?"
	// 			);

	// 			if (!shouldLeave) {
	// 				// Push the current state back to prevent navigation
	// 				window.history.pushState(null, "", window.location.href);
	// 				isNavigationBlocked = false;
	// 				return;
	// 			}
	// 		}
	// 		isNavigationBlocked = false;
	// 	};

	// 	// Intercept all link clicks and navigation attempts
	// 	const handleLinkClick = (event: MouseEvent) => {
	// 		const target = event.target as HTMLElement;
	// 		const link = target.closest('a');

	// 		if (link && test && testStarted && !testTimesUp && !showResults) {
	// 			const shouldLeave = window.confirm(
	// 				"You will lose your test progress if you leave this page. Are you sure you want to continue?"
	// 			);

	// 			if (!shouldLeave) {
	// 				event.preventDefault();
	// 				event.stopPropagation();
	// 			}
	// 		}
	// 	};

	// 	// Push initial state to enable popstate detection
	// 	if (test && testStarted && !testTimesUp && !showResults) {
	// 		window.history.pushState(null, "", window.location.href);
	// 		window.addEventListener("popstate", handlePopState);
	// 		document.addEventListener("click", handleLinkClick, true);
	// 	}

	// 	return () => {
	// 		window.removeEventListener("popstate", handlePopState);
	// 		document.removeEventListener("click", handleLinkClick, true);
	// 	};
	// }, [test, testStarted, testTimesUp, showResults]);

	useEffect(() => {
		if (test && liveCheckEnabled && Object.keys(selectedAnswers).length > 0) {
			const newStatus: Record<string, boolean> = {};
			Object.entries(selectedAnswers).forEach(([questionId, optionIndex]) => {
				const mcq = test.mcqs.find((m) => m.id === questionId);
				if (mcq) {
					newStatus[questionId] = optionIndex === mcq.correctAnswer;
				}
			});

			setAnswerStatus(newStatus);
			// setShowResults(true);
		}
	}, [liveCheckEnabled, selectedAnswers, test]);

	const handleAnswerSelect = (questionId: string, optionIndex: number) => {
		if (!test || (showResults && selectedAnswers[questionId] !== undefined))
			return;

		const mcq = test.mcqs.find((m) => m.id === questionId);
		if (!mcq) return;

		setSelectedAnswers((prev) => ({
			...prev,
			[questionId]: optionIndex,
		}));

		if (liveCheckEnabled) {
			// Check if the selected answer matches the correctAnswer in the MCQ
			const isCorrect = optionIndex === mcq.correctAnswer;
			setAnswerStatus((prev) => ({
				...prev,
				[questionId]: isCorrect,
			}));
			// setShowResults(true);
		}
	};

	const handleCheckAnswers = () => {
		if (!test) return;

		const newStatus: Record<string, boolean> = {};

		Object.entries(selectedAnswers).forEach(([questionId, optionIndex]) => {
			const mcq = test.mcqs.find((m) => m.id === questionId);
			if (mcq) {
				// Compare the selected answer with the correct answer
				newStatus[questionId] = optionIndex === mcq.correctAnswer;
			}
		});

		setAnswerStatus(newStatus);
		setShowResults(true);
	};

	console.log(test, "test");

	// Show static component if no test data is available
	if (!test) {
		return <MCQStaticComponent />;
	}

	return isDesktop ? (
		<TestLayout
			test={test}
			liveCheckEnabled={liveCheckEnabled}
			setLiveCheckEnabled={setLiveCheckEnabled}
			selectedAnswers={selectedAnswers}
			answerStatus={answerStatus}
			showResults={showResults}
			onAnswerSelect={handleAnswerSelect}
			onCheckAnswers={handleCheckAnswers}
			testTimesUp={testTimesUp}
			setTestTimesUp={setTestTimesUp}
		/>
	) : (
		<MCQMobileLayout
			test={test}
			liveCheckEnabled={liveCheckEnabled}
			setLiveCheckEnabled={setLiveCheckEnabled}
			selectedAnswers={selectedAnswers}
			answerStatus={answerStatus}
			showResults={showResults}
			onAnswerSelect={handleAnswerSelect}
			onCheckAnswers={handleCheckAnswers}
			testTimesUp={testTimesUp}
		/>
	);
};

export const Route = createFileRoute("/(app)/_mainlayout/mcqs")({
	component: Page,
});

export default Page;
