"use client";

import { cn } from "@/lib/utils";
import { routeList } from "@/lib/route-list";
import { But<PERSON> } from "@/components/ui/button";
import {
	Toolt<PERSON>,
	TooltipTrigger,
	TooltipContent,
	TooltipProvider,
} from "@/components/ui/tooltip";
import { CollapseMenuButton } from "./collapse-menu-button";
import { MoreVertical } from "react-feather";
import { Link } from "@tanstack/react-router";
import NeedHelp from "./need-help";
import { useAuthStore } from "@/features/auth/store";
import { useShallow } from "zustand/react/shallow";
import { useState } from "react";

interface MenuProps {
	isOpen: boolean | undefined;
}

export function Menu({ isOpen }: MenuProps) {
	const { logout } = useAuthStore(
		useShallow((state) => ({
			logout: state.logout,
		}))
	);
	const pathname = window.location.pathname;
	const menuList = routeList.filter((menu) => !menu.exclude);
	const [currentTab, SetCurrentTab] = useState<string>("Dashboard");

	const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
		const name = e.currentTarget.name;
		SetCurrentTab(name);
		switch (name) {
			case "Logout":
				logout();
				break;
			default:
				break;
		}
	};

	const isComingSoon = (label: string) => {
		return (
			label !== "Mock Test" &&
			label !== "Dashboard" &&
			label !== "Analytics" &&
			label !== "Resources" &&
			label !== "Logout"
		);
	};

	return (
		<nav className="lg:mt-20 flex flex-col w-full min-h-[calc(100vh-48px-36px-16px-32px)] lg:min-h-[calc(100vh-32px-40px-32px)]">
			<ul className="flex flex-col grow items-start space-y-2 px-2">
				{menuList.map(({ groupLabel, menus }, index) => (
					<li
						className={cn(
							"flex w-full flex-col lg:gap-y-2",
							groupLabel ? "pt-5" : index !== 0 && "border-t"
						)}
						key={index}
					>
						{(isOpen && groupLabel) || isOpen === undefined ? (
							<p className="text-sm font-bold text-gray-400 px-4 pb-2 max-w-[248px] uppercase truncate dark:text-slate-400">
								{groupLabel}
							</p>
						) : !isOpen && isOpen !== undefined && groupLabel ? (
							<TooltipProvider>
								<Tooltip delayDuration={100}>
									<TooltipTrigger className="w-full">
										<div className="w-full flex justify-center items-center">
											<MoreVertical className="h-5 w-5" />
										</div>
									</TooltipTrigger>
									<TooltipContent side="right">
										<p>{groupLabel}</p>
									</TooltipContent>
								</Tooltip>
							</TooltipProvider>
						) : (
							<p className="pb-0"></p>
						)}
						{menus.map(
							({ href, label, icon: Icon, active, submenus }, index) =>
								!submenus || submenus.length === 0 ? (
									<div className="w-full relative" key={index}>
										<TooltipProvider disableHoverableContent>
											<Tooltip delayDuration={100}>
												<TooltipTrigger asChild>
													<Button
														variant={
															(currentTab?.toLowerCase() ===
																label?.toLowerCase() &&
																!isComingSoon(label)) ||
															active
																? "active"
																: "inactive"
														}
														className={cn(
															"rounded-full text-base w-full justify-start gap-3 h-11",
															isComingSoon(label) &&
																"opacity-70 cursor-not-allowed"
														)}
														value={label}
														name={label}
														onClick={
															isComingSoon(label) ? undefined : handleClick
														}
														asChild={href.length > 0 && !isComingSoon(label)}
														disabled={isComingSoon(label)}
													>
														{href.length > 0 && !isComingSoon(label) ? (
															<Link to={href}>
																<span
																	className={cn(isOpen === false ? "" : "mr-0")}
																>
																	<Icon size={24} />
																</span>
																<p
																	className={cn(
																		"max-w-[200px] truncate",
																		isOpen === false
																			? "-translate-x-96 opacity-0"
																			: "translate-x-0 opacity-100"
																	)}
																>
																	{label}
																</p>
															</Link>
														) : (
															<>
																<span
																	className={cn(isOpen === false ? "" : "mr-0")}
																>
																	<Icon size={24} />
																</span>
																<p
																	className={cn(
																		"max-w-[200px] truncate",
																		isOpen === false
																			? "-translate-x-96 opacity-0"
																			: "translate-x-0 opacity-100"
																	)}
																>
																	{label}
																</p>
															</>
														)}
													</Button>
												</TooltipTrigger>
												{isOpen === false && (
													<TooltipContent side="right">
														{label}
														{isComingSoon(label) && (
															<span className="ml-2 text-xs py-0.5 px-1 border border-accent text-accent rounded-md">
																Coming Soon
															</span>
														)}
													</TooltipContent>
												)}
											</Tooltip>
										</TooltipProvider>

										{isComingSoon(label) && isOpen !== false && (
											<div className="absolute right-0 top-1/2 -translate-y-1/2 text-xs py-0.5 px-1 border border-accent text-accent rounded-md whitespace-nowrap">
												Coming Soon
											</div>
										)}
									</div>
								) : (
									<div className="w-full" key={index}>
										<CollapseMenuButton
											icon={Icon}
											label={label}
											active={
												active === undefined
													? pathname.startsWith(href)
													: active
											}
											submenus={submenus}
											isOpen={isOpen}
										/>
									</div>
								)
						)}
					</li>
				))}
			</ul>

			<div className={cn("px-4", !isOpen && "hidden")}>
				{/* <UpgradePlan /> */}
				<NeedHelp />
			</div>
		</nav>
	);
}
