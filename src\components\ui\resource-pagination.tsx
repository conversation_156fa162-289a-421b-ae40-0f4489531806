import React from "react";
import {
	Pa<PERSON><PERSON>,
	PaginationContent,
	PaginationEllipsis,
	PaginationItem,
	PaginationLink,
	PaginationNext,
	PaginationPrevious,
} from "@/components/ui/pagination";
import { PaginationInfo } from "@/features/resources/types";

interface ResourcePaginationProps {
	pagination: PaginationInfo;
	onPageChange: (page: number) => void;
	className?: string;
}

const ResourcePagination: React.FC<ResourcePaginationProps> = ({
	pagination,
	onPageChange,
	className,
}) => {
	const { currentPage, totalPages, hasPrev, hasNext } = pagination;

	// Generate page numbers to display
	const getPageNumbers = () => {
		const pages: (number | "ellipsis")[] = [];
		const maxVisiblePages = 5;

		if (totalPages <= maxVisiblePages) {
			// Show all pages if total pages is small
			for (let i = 1; i <= totalPages; i++) {
				pages.push(i);
			}
		} else {
			// Always show first page
			pages.push(1);

			if (currentPage > 3) {
				pages.push("ellipsis");
			}

			// Show pages around current page
			const start = Math.max(2, currentPage - 1);
			const end = Math.min(totalPages - 1, currentPage + 1);

			for (let i = start; i <= end; i++) {
				if (i !== 1 && i !== totalPages) {
					pages.push(i);
				}
			}

			if (currentPage < totalPages - 2) {
				pages.push("ellipsis");
			}

			// Always show last page if more than 1 page
			if (totalPages > 1) {
				pages.push(totalPages);
			}
		}

		return pages;
	};

	const pageNumbers = getPageNumbers();

	if (totalPages <= 1) {
		return null; // Don't show pagination if there's only one page or no pages
	}

	return (
		<Pagination className={className}>
			<PaginationContent>
				<PaginationItem>
					<PaginationPrevious
						onClick={() => onPageChange(currentPage - 1)}
						disabled={!hasPrev}
						className={!hasPrev ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}
					/>
				</PaginationItem>

				{pageNumbers.map((page, index) => (
					<PaginationItem key={index}>
						{page === "ellipsis" ? (
							<PaginationEllipsis />
						) : (
							<PaginationLink
								onClick={() => onPageChange(page)}
								isActive={page === currentPage}
								className="cursor-pointer"
							>
								{page}
							</PaginationLink>
						)}
					</PaginationItem>
				))}

				<PaginationItem>
					<PaginationNext
						onClick={() => onPageChange(currentPage + 1)}
						disabled={!hasNext}
						className={!hasNext ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}
					/>
				</PaginationItem>
			</PaginationContent>
		</Pagination>
	);
};

export default ResourcePagination;
