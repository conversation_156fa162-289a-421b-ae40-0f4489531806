import { Button } from "@/components/ui/button";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
} from "@/components/ui/command";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import { Check, ChevronDown, X } from "react-feather";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { useState } from "react";
import { useGetFilters } from "@/lib/queries/resources.query";
import { FilterParams } from "@/features/resources/types";
import FilterSkeleton from "@/components/ui/filter-skeleton";

interface EducationFilterProps {
	subject: string;
	resourceType: string;
	onFiltersChange?: (filters: FilterParams) => void;
}

const EducationFilter = ({ subject, resourceType, onFiltersChange }: EducationFilterProps) => {
	const [selectedEntryTests, setSelectedEntryTests] = useState(() => new Set<string>());
	const [selectedChapters, setSelectedChapters] = useState(() => new Set<string>());
	const [selectedTopics, setSelectedTopics] = useState(() => new Set<string>());
	const [selectedLanguages, setSelectedLanguages] = useState(() => new Set<string>());
	const [selectedCategories, setSelectedCategories] = useState(() => new Set<string>());

	const [entryTestsOpen, setEntryTestsOpen] = useState(false);
	const [chaptersOpen, setChaptersOpen] = useState(false);
	const [topicsOpen, setTopicsOpen] = useState(false);
	const [languagesOpen, setLanguagesOpen] = useState(false);
	const [categoriesOpen, setCategoriesOpen] = useState(false);

	// Get current filters for API call
	const currentFilters: FilterParams = {
		entryTests: Array.from(selectedEntryTests),
		chapters: Array.from(selectedChapters),
		topics: Array.from(selectedTopics),
		language: Array.from(selectedLanguages),
		categories: Array.from(selectedCategories),
	};

	// Check if any filters are selected
	const hasFilters = currentFilters.entryTests?.length ||
		currentFilters.chapters?.length ||
		currentFilters.topics?.length ||
		currentFilters.language?.length ||
		currentFilters.categories?.length;

	// Fetch filter options from API (don't pass filters initially to get all options)
	const { data: filterData, isLoading: isLoadingFilters } = useGetFilters(
		subject,
		resourceType,
		hasFilters ? currentFilters : undefined
	);

	const handleFilterChange = (updatedState?: {
		entryTests?: Set<string>;
		chapters?: Set<string>;
		topics?: Set<string>;
		languages?: Set<string>;
		categories?: Set<string>;
	}) => {
		if (onFiltersChange) {
			const filters: FilterParams = {
				entryTests: Array.from(updatedState?.entryTests || selectedEntryTests),
				chapters: Array.from(updatedState?.chapters || selectedChapters),
				topics: Array.from(updatedState?.topics || selectedTopics),
				language: Array.from(updatedState?.languages || selectedLanguages),
				categories: Array.from(updatedState?.categories || selectedCategories),
			};
			console.log(filters, "__________currentFilters");
			onFiltersChange(filters);
		}
	};

	// Show skeleton while loading
	if (isLoadingFilters) {
		return <FilterSkeleton />;
	}

	// Use API data and convert to the expected format
	// API returns: { entryTest: [], chapters: [], topic: [], langs: [], categories: [] }
	const apiData = filterData as any;

	// Convert API response to FilterOption format
	const entryTestOptions = (apiData?.entryTest || []).map((item: string) => ({
		label: item,
		value: item
	}));

	const chapterOptions = (apiData?.chapters || []).map((item: string) => ({
		label: item,
		value: item
	}));

	const topicOptions = (apiData?.topic || []).map((item: string) => ({
		label: item,
		value: item
	}));

	const languageOptions = (apiData?.langs || []).map((item: string) => ({
		label: item,
		value: item
	}));

	const categoryOptions = (apiData?.categories || []).map((item: string) => ({
		label: item,
		value: item
	}));

	const clearAllFilters = () => {
		setSelectedEntryTests(new Set());
		setSelectedChapters(new Set());
		setSelectedTopics(new Set());
		setSelectedLanguages(new Set());
		setSelectedCategories(new Set());
		if (onFiltersChange) {
			onFiltersChange({
				entryTests: [],
				chapters: [],
				topics: [],
				language: [],
				categories: [],
			});
		}
	};

	const FilterDropdown = ({
		title,
		options,
		selectedItems,
		setSelectedItems,
		isOpen,
		setIsOpen,
		placeholder = "Search...",
	}: {
		title: string;
		options: { label: string; value: string }[];
		selectedItems: Set<string>;
		setSelectedItems: React.Dispatch<React.SetStateAction<Set<string>>>;
		isOpen: boolean;
		setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
		placeholder?: string;
	}) => {
		const isEmpty = options.length === 0;

		return (
			<div className="flex flex-col space-y-1">
				<label className={`text-xs font-medium ${isEmpty ? 'text-gray-300' : 'text-gray-500'}`}>
					{title}
				</label>
				<Popover open={!isEmpty && isOpen} onOpenChange={isEmpty ? undefined : setIsOpen}>
					<PopoverTrigger asChild>
						<div className={`relative flex min-h-[36px] items-center justify-end rounded-md border ${
							isEmpty
								? 'border-gray-200 bg-gray-50 cursor-not-allowed'
								: 'data-[state=open]:border-ring cursor-pointer'
						}`}>
						<div className="relative mr-auto flex flex-grow flex-wrap items-center overflow-hidden px-3 py-1">
							{isEmpty ? (
								<span className="text-gray-400 text-sm">No options available</span>
							) : selectedItems.size > 0 ? (
								options
									.filter((option) => selectedItems.has(option.value))
									.map((option) => (
										<Badge
											key={option.value}
											variant="secondary"
											className="mr-1 mb-1 text-[10px] px-1 py-0"
										>
											{option.label}
											<button
												className="ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
												onKeyDown={(e) => {
													if (e.key === "Enter") {
														selectedItems.delete(option.value);
														const newSet = new Set(selectedItems);
														setSelectedItems(newSet);
														// Determine which filter type this is and pass updated state
														if (title === "Entry Tests") {
															handleFilterChange({ entryTests: newSet });
														} else if (title === "Chapters") {
															handleFilterChange({ chapters: newSet });
														} else if (title === "Topics") {
															handleFilterChange({ topics: newSet });
														} else if (title === "Language") {
															handleFilterChange({ languages: newSet });
														} else if (title === "Categories") {
															handleFilterChange({ categories: newSet });
														}
													}
												}}
												onMouseDown={(e) => {
													e.preventDefault();
													e.stopPropagation();
												}}
												onClick={(e) => {
													e.preventDefault();
													e.stopPropagation();
													selectedItems.delete(option.value);
													const newSet = new Set(selectedItems);
													setSelectedItems(newSet);
													// Determine which filter type this is and pass updated state
													if (title === "Entry Tests") {
														handleFilterChange({ entryTests: newSet });
													} else if (title === "Chapters") {
														handleFilterChange({ chapters: newSet });
													} else if (title === "Topics") {
														handleFilterChange({ topics: newSet });
													} else if (title === "Language") {
														handleFilterChange({ languages: newSet });
													} else if (title === "Categories") {
														handleFilterChange({ categories: newSet });
													}
												}}
											>
												<X className="h-3 w-3 text-muted-foreground hover:text-foreground" />
											</button>
										</Badge>
									))
							) : (
								<span className="text-muted-foreground text-sm">
									Select {title.toLowerCase()}...
								</span>
							)}
						</div>
						<span className="mx-0.5 my-2 w-[1px] self-stretch bg-border" />
						<div className={`flex items-center self-stretch p-2 ${
							isEmpty ? 'text-gray-300' : 'hover:text-muted-foreground'
						}`}>
							<ChevronDown size={16} />
						</div>
					</div>
				</PopoverTrigger>
				<PopoverContent
					className="w-[var(--radix-popover-trigger-width)] p-0 max-h-80"
					align="start"
				>
					<Command>
						<CommandInput placeholder={placeholder} className="h-9" />
						<CommandEmpty>No results found.</CommandEmpty>
						<CommandGroup className="max-h-60 overflow-y-auto">
							{options.map((option, index) => {
								const isSelected = selectedItems.has(option.value);
								return (
									<CommandItem
										key={index}
										onSelect={() => {
											if (isSelected) {
												selectedItems.delete(option.value);
											} else {
												selectedItems.add(option.value);
											}
											const newSet = new Set(selectedItems);
											setSelectedItems(newSet);
											// Determine which filter type this is and pass updated state
											if (title === "Entry Tests") {
												handleFilterChange({ entryTests: newSet });
											} else if (title === "Chapters") {
												handleFilterChange({ chapters: newSet });
											} else if (title === "Topics") {
												handleFilterChange({ topics: newSet });
											} else if (title === "Language") {
												handleFilterChange({ languages: newSet });
											} else if (title === "Categories") {
												handleFilterChange({ categories: newSet });
											}
										}}
									>
										<div
											className={cn(
												"mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
												isSelected
													? "bg-primary text-primary-foreground"
													: "opacity-50 [&_svg]:invisible"
											)}
										>
											<Check className={cn("h-4 w-4")} />
										</div>
										<span>{option.label}</span>
									</CommandItem>
								);
							})}
						</CommandGroup>
					</Command>
				</PopoverContent>
			</Popover>
		</div>
	);
};

return (
		<div className="space-y-3 p-3 bg-white rounded-lg border">
			<div className="flex items-center justify-between">
				<Button
					variant="outline"
					size="sm"
					onClick={clearAllFilters}
					className="text-xs w-full"
				>
					Clear All Filters
				</Button>
			</div>

			<div className="space-y-3">
				<FilterDropdown
					title="Entry Tests"
					options={entryTestOptions}
					selectedItems={selectedEntryTests}
					setSelectedItems={setSelectedEntryTests}
					isOpen={entryTestsOpen}
					setIsOpen={setEntryTestsOpen}
					placeholder="Search entry tests..."
				/>

				<FilterDropdown
					title="Chapters"
					options={chapterOptions}
					selectedItems={selectedChapters}
					setSelectedItems={setSelectedChapters}
					isOpen={chaptersOpen}
					setIsOpen={setChaptersOpen}
					placeholder="Search chapters..."
				/>

				<FilterDropdown
					title="Topics"
					options={topicOptions}
					selectedItems={selectedTopics}
					setSelectedItems={setSelectedTopics}
					isOpen={topicsOpen}
					setIsOpen={setTopicsOpen}
					placeholder="Search topics..."
				/>

				<FilterDropdown
					title="Language"
					options={languageOptions}
					selectedItems={selectedLanguages}
					setSelectedItems={setSelectedLanguages}
					isOpen={languagesOpen}
					setIsOpen={setLanguagesOpen}
					placeholder="Search languages..."
				/>

				<FilterDropdown
					title="Categories"
					options={categoryOptions}
					selectedItems={selectedCategories}
					setSelectedItems={setSelectedCategories}
					isOpen={categoriesOpen}
					setIsOpen={setCategoriesOpen}
					placeholder="Search categories..."
				/>
			</div>
		</div>
	);
};

export default EducationFilter;
