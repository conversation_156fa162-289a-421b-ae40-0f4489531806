import { create } from "zustand";
import {
	signInWithEmailAndPassword,
	signOut,
	signInWithPopup,
	GoogleAuthProvider,
	FacebookAuthProvider,
	type User,
	createUserWithEmailAndPassword,
	UserCredential,
	updateProfile,
	sendEmailVerification,
} from "firebase/auth";
import auth from "@/lib/config/firebase";
import { redirect } from "@tanstack/react-router";

interface AuthState {
	user: User | null;
	isNewUser: boolean;
	firebaseToken: string | null;
	isLoggedIn: boolean;
	loading: boolean;
	checkingAuth: boolean;
	error: string | null;
	login: (email: string, password: string) => Promise<UserCredential | Error>;
	register: (
		name: string,
		email: string,
		password: string
	) => Promise<UserCredential | undefined>;
	logout: () => Promise<void>;
	loginWithGoogle: () => Promise<UserCredential | Error>;
	loginWithFacebook: () => Promise<void>;
	sendFirebaseEmailVerification: (user: any) => Promise<void>;
	setUser: (user: any) => void;
	setLoggedIn: (isLoggedIn: boolean) => void;
	setLoading: (loading: boolean) => void;
	setError: (error: string | null) => void;
	setCheckingAuth: (checkingAuth: boolean) => void;
	setIsNewUser: (isNewUser: boolean) => void;
	setFirebaseToken: (firebaseToken: string | null) => void;
}

export const emailVerificationConfig = {
	url:
		import.meta.env["VITE_APP_ENVIRONMENT"] === "development"
			? "http://localhost:5173/account/login"
			: import.meta.env["VITE_APP_ENVIRONMENT"] === "preview"
				? "https://app-preview.parhlai.com/account/login"
				: "https://app.parhlai.com/account/login",
};

export const useAuthStore = create<AuthState>((set) => ({
	user: null,
	firebaseToken: null,
	isNewUser: false,
	isLoggedIn: false,
	authInitialized: false,
	loading: true,
	error: null,
	checkingAuth: true,
	login: async (email, password) => {
		set({ loading: true, error: null });
		try {
			const result = await signInWithEmailAndPassword(auth, email, password);
			set({ firebaseToken: await result.user.getIdToken() });
			return result;
		} catch (error: any) {
			set({ error: error.message });
			return error;
		} finally {
			set({ loading: false });
		}
	},
	sendFirebaseEmailVerification: async (user: any) => {
		await sendEmailVerification(user, emailVerificationConfig);
	},
	register: async (name, email, password) => {
		set({ loading: true, error: null });
		try {
			const result = await createUserWithEmailAndPassword(
				auth,
				email,
				password
			);
			await updateProfile(result.user, {
				displayName: name,
			});
			await sendEmailVerification(result.user, emailVerificationConfig);
			set({ firebaseToken: await result.user.getIdToken(), isNewUser: true });
			return result;
		} catch (error: any) {
			set({ error: error.message });
			return error;
		} finally {
			set({ loading: false });
		}
	},
	logout: async () => {
		set({ loading: true, error: null });
		try {
			await signOut(auth);
			set({ isLoggedIn: false });
			redirect({ to: "/account/login" });
		} catch (error: any) {
			set({ error: error.message });
		} finally {
			set({ loading: false });
		}
	},
	loginWithGoogle: async () => {
		set({ loading: true, error: null });
		try {
			const provider = new GoogleAuthProvider();
			const result = await signInWithPopup(auth, provider);
			const token = await result.user.getIdToken();
			set({ firebaseToken: token });
			return result;
		} catch (error: any) {
			set({ error: error.message });
			return error;
		} finally {
			set({ loading: false });
		}
	},
	loginWithFacebook: async () => {
		set({ loading: true, error: null });
		try {
			const provider = new FacebookAuthProvider();
			const result = await signInWithPopup(auth, provider);
			set({ firebaseToken: await result.user.getIdToken() });
		} catch (error: any) {
			set({ error: error.message });
		} finally {
			set({ loading: false });
		}
	},
	setUser: (user) => set({ user }),
	setLoggedIn: (isLoggedIn) => set({ isLoggedIn }),
	setLoading: (loading) => set({ loading }),
	setError: (error) => set({ error }),
	setCheckingAuth: (checkingAuth) => set({ checkingAuth }),
	setIsNewUser: (isNewUser) => set({ isNewUser }),
	setFirebaseToken: (firebaseToken) => set({ firebaseToken }),
}));
