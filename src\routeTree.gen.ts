/* prettier-ignore-start */

/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file is auto-generated by TanStack Router

import { createFileRoute } from '@tanstack/react-router'

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as IndexImport } from './routes/index'
import { Route as AccountLayoutImport } from './routes/account/_layout'
import { Route as appMainlayoutImport } from './routes/(app)/_mainlayout'
import { Route as appPricingSelectplanImport } from './routes/(app)/_pricing/selectplan'
import { Route as appMainlayoutMcqsImport } from './routes/(app)/_mainlayout/mcqs'
import { Route as appMainlayoutLearnImport } from './routes/(app)/_mainlayout/learn'
import { Route as appMainlayoutEditAccountImport } from './routes/(app)/_mainlayout/edit-account'
import { Route as appMainlayoutDashboardImport } from './routes/(app)/_mainlayout/dashboard'
import { Route as appMainlayoutAnalyticsImport } from './routes/(app)/_mainlayout/analytics'
import { Route as appMainlayoutAccountImport } from './routes/(app)/_mainlayout/account'
import { Route as appMainlayoutTMockImport } from './routes/(app)/_mainlayout/t/mock'

// Create Virtual Routes

const AccountImport = createFileRoute('/account')()
const appImport = createFileRoute('/(app)')()
const AccountLayoutVerifyemailLazyImport = createFileRoute(
  '/account/_layout/verifyemail',
)()
const AccountLayoutResetpasswordLazyImport = createFileRoute(
  '/account/_layout/resetpassword',
)()
const AccountLayoutRegisterLazyImport = createFileRoute(
  '/account/_layout/register',
)()
const AccountLayoutPersonalinfoLazyImport = createFileRoute(
  '/account/_layout/personalinfo',
)()
const AccountLayoutLoginLazyImport = createFileRoute('/account/_layout/login')()

// Create/Update Routes

const AccountRoute = AccountImport.update({
  id: '/account',
  path: '/account',
  getParentRoute: () => rootRoute,
} as any)

const appRoute = appImport.update({
  id: '/(app)',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const AccountLayoutRoute = AccountLayoutImport.update({
  id: '/_layout',
  getParentRoute: () => AccountRoute,
} as any)

const appMainlayoutRoute = appMainlayoutImport.update({
  id: '/_mainlayout',
  getParentRoute: () => appRoute,
} as any)

const AccountLayoutVerifyemailLazyRoute =
  AccountLayoutVerifyemailLazyImport.update({
    id: '/verifyemail',
    path: '/verifyemail',
    getParentRoute: () => AccountLayoutRoute,
  } as any).lazy(() =>
    import('./routes/account/_layout.verifyemail.lazy').then((d) => d.Route),
  )

const AccountLayoutResetpasswordLazyRoute =
  AccountLayoutResetpasswordLazyImport.update({
    id: '/resetpassword',
    path: '/resetpassword',
    getParentRoute: () => AccountLayoutRoute,
  } as any).lazy(() =>
    import('./routes/account/_layout.resetpassword.lazy').then((d) => d.Route),
  )

const AccountLayoutRegisterLazyRoute = AccountLayoutRegisterLazyImport.update({
  id: '/register',
  path: '/register',
  getParentRoute: () => AccountLayoutRoute,
} as any).lazy(() =>
  import('./routes/account/_layout.register.lazy').then((d) => d.Route),
)

const AccountLayoutPersonalinfoLazyRoute =
  AccountLayoutPersonalinfoLazyImport.update({
    id: '/personalinfo',
    path: '/personalinfo',
    getParentRoute: () => AccountLayoutRoute,
  } as any).lazy(() =>
    import('./routes/account/_layout.personalinfo.lazy').then((d) => d.Route),
  )

const AccountLayoutLoginLazyRoute = AccountLayoutLoginLazyImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => AccountLayoutRoute,
} as any).lazy(() =>
  import('./routes/account/_layout.login.lazy').then((d) => d.Route),
)

const appPricingSelectplanRoute = appPricingSelectplanImport.update({
  id: '/_pricing/selectplan',
  path: '/selectplan',
  getParentRoute: () => appRoute,
} as any)

const appMainlayoutMcqsRoute = appMainlayoutMcqsImport.update({
  id: '/mcqs',
  path: '/mcqs',
  getParentRoute: () => appMainlayoutRoute,
} as any)

const appMainlayoutLearnRoute = appMainlayoutLearnImport.update({
  id: '/learn',
  path: '/learn',
  getParentRoute: () => appMainlayoutRoute,
} as any)

const appMainlayoutEditAccountRoute = appMainlayoutEditAccountImport.update({
  id: '/edit-account',
  path: '/edit-account',
  getParentRoute: () => appMainlayoutRoute,
} as any)

const appMainlayoutDashboardRoute = appMainlayoutDashboardImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => appMainlayoutRoute,
} as any)

const appMainlayoutAnalyticsRoute = appMainlayoutAnalyticsImport.update({
  id: '/analytics',
  path: '/analytics',
  getParentRoute: () => appMainlayoutRoute,
} as any)

const appMainlayoutAccountRoute = appMainlayoutAccountImport.update({
  id: '/account',
  path: '/account',
  getParentRoute: () => appMainlayoutRoute,
} as any)

const appMainlayoutTMockRoute = appMainlayoutTMockImport.update({
  id: '/t/mock',
  path: '/t/mock',
  getParentRoute: () => appMainlayoutRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/(app)': {
      id: '/(app)'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof appImport
      parentRoute: typeof rootRoute
    }
    '/(app)/_mainlayout': {
      id: '/(app)/_mainlayout'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof appMainlayoutImport
      parentRoute: typeof appRoute
    }
    '/account': {
      id: '/account'
      path: '/account'
      fullPath: '/account'
      preLoaderRoute: typeof AccountImport
      parentRoute: typeof rootRoute
    }
    '/account/_layout': {
      id: '/account/_layout'
      path: '/account'
      fullPath: '/account'
      preLoaderRoute: typeof AccountLayoutImport
      parentRoute: typeof AccountRoute
    }
    '/(app)/_mainlayout/account': {
      id: '/(app)/_mainlayout/account'
      path: '/account'
      fullPath: '/account'
      preLoaderRoute: typeof appMainlayoutAccountImport
      parentRoute: typeof appMainlayoutImport
    }
    '/(app)/_mainlayout/analytics': {
      id: '/(app)/_mainlayout/analytics'
      path: '/analytics'
      fullPath: '/analytics'
      preLoaderRoute: typeof appMainlayoutAnalyticsImport
      parentRoute: typeof appMainlayoutImport
    }
    '/(app)/_mainlayout/dashboard': {
      id: '/(app)/_mainlayout/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof appMainlayoutDashboardImport
      parentRoute: typeof appMainlayoutImport
    }
    '/(app)/_mainlayout/edit-account': {
      id: '/(app)/_mainlayout/edit-account'
      path: '/edit-account'
      fullPath: '/edit-account'
      preLoaderRoute: typeof appMainlayoutEditAccountImport
      parentRoute: typeof appMainlayoutImport
    }
    '/(app)/_mainlayout/learn': {
      id: '/(app)/_mainlayout/learn'
      path: '/learn'
      fullPath: '/learn'
      preLoaderRoute: typeof appMainlayoutLearnImport
      parentRoute: typeof appMainlayoutImport
    }
    '/(app)/_mainlayout/mcqs': {
      id: '/(app)/_mainlayout/mcqs'
      path: '/mcqs'
      fullPath: '/mcqs'
      preLoaderRoute: typeof appMainlayoutMcqsImport
      parentRoute: typeof appMainlayoutImport
    }
    '/(app)/_pricing/selectplan': {
      id: '/(app)/_pricing/selectplan'
      path: '/selectplan'
      fullPath: '/selectplan'
      preLoaderRoute: typeof appPricingSelectplanImport
      parentRoute: typeof appImport
    }
    '/account/_layout/login': {
      id: '/account/_layout/login'
      path: '/login'
      fullPath: '/account/login'
      preLoaderRoute: typeof AccountLayoutLoginLazyImport
      parentRoute: typeof AccountLayoutImport
    }
    '/account/_layout/personalinfo': {
      id: '/account/_layout/personalinfo'
      path: '/personalinfo'
      fullPath: '/account/personalinfo'
      preLoaderRoute: typeof AccountLayoutPersonalinfoLazyImport
      parentRoute: typeof AccountLayoutImport
    }
    '/account/_layout/register': {
      id: '/account/_layout/register'
      path: '/register'
      fullPath: '/account/register'
      preLoaderRoute: typeof AccountLayoutRegisterLazyImport
      parentRoute: typeof AccountLayoutImport
    }
    '/account/_layout/resetpassword': {
      id: '/account/_layout/resetpassword'
      path: '/resetpassword'
      fullPath: '/account/resetpassword'
      preLoaderRoute: typeof AccountLayoutResetpasswordLazyImport
      parentRoute: typeof AccountLayoutImport
    }
    '/account/_layout/verifyemail': {
      id: '/account/_layout/verifyemail'
      path: '/verifyemail'
      fullPath: '/account/verifyemail'
      preLoaderRoute: typeof AccountLayoutVerifyemailLazyImport
      parentRoute: typeof AccountLayoutImport
    }
    '/(app)/_mainlayout/t/mock': {
      id: '/(app)/_mainlayout/t/mock'
      path: '/t/mock'
      fullPath: '/t/mock'
      preLoaderRoute: typeof appMainlayoutTMockImport
      parentRoute: typeof appMainlayoutImport
    }
  }
}

// Create and export the route tree

interface appMainlayoutRouteChildren {
  appMainlayoutAccountRoute: typeof appMainlayoutAccountRoute
  appMainlayoutAnalyticsRoute: typeof appMainlayoutAnalyticsRoute
  appMainlayoutDashboardRoute: typeof appMainlayoutDashboardRoute
  appMainlayoutEditAccountRoute: typeof appMainlayoutEditAccountRoute
  appMainlayoutLearnRoute: typeof appMainlayoutLearnRoute
  appMainlayoutMcqsRoute: typeof appMainlayoutMcqsRoute
  appMainlayoutTMockRoute: typeof appMainlayoutTMockRoute
}

const appMainlayoutRouteChildren: appMainlayoutRouteChildren = {
  appMainlayoutAccountRoute: appMainlayoutAccountRoute,
  appMainlayoutAnalyticsRoute: appMainlayoutAnalyticsRoute,
  appMainlayoutDashboardRoute: appMainlayoutDashboardRoute,
  appMainlayoutEditAccountRoute: appMainlayoutEditAccountRoute,
  appMainlayoutLearnRoute: appMainlayoutLearnRoute,
  appMainlayoutMcqsRoute: appMainlayoutMcqsRoute,
  appMainlayoutTMockRoute: appMainlayoutTMockRoute,
}

const appMainlayoutRouteWithChildren = appMainlayoutRoute._addFileChildren(
  appMainlayoutRouteChildren,
)

interface appRouteChildren {
  appMainlayoutRoute: typeof appMainlayoutRouteWithChildren
  appPricingSelectplanRoute: typeof appPricingSelectplanRoute
}

const appRouteChildren: appRouteChildren = {
  appMainlayoutRoute: appMainlayoutRouteWithChildren,
  appPricingSelectplanRoute: appPricingSelectplanRoute,
}

const appRouteWithChildren = appRoute._addFileChildren(appRouteChildren)

interface AccountLayoutRouteChildren {
  AccountLayoutLoginLazyRoute: typeof AccountLayoutLoginLazyRoute
  AccountLayoutPersonalinfoLazyRoute: typeof AccountLayoutPersonalinfoLazyRoute
  AccountLayoutRegisterLazyRoute: typeof AccountLayoutRegisterLazyRoute
  AccountLayoutResetpasswordLazyRoute: typeof AccountLayoutResetpasswordLazyRoute
  AccountLayoutVerifyemailLazyRoute: typeof AccountLayoutVerifyemailLazyRoute
}

const AccountLayoutRouteChildren: AccountLayoutRouteChildren = {
  AccountLayoutLoginLazyRoute: AccountLayoutLoginLazyRoute,
  AccountLayoutPersonalinfoLazyRoute: AccountLayoutPersonalinfoLazyRoute,
  AccountLayoutRegisterLazyRoute: AccountLayoutRegisterLazyRoute,
  AccountLayoutResetpasswordLazyRoute: AccountLayoutResetpasswordLazyRoute,
  AccountLayoutVerifyemailLazyRoute: AccountLayoutVerifyemailLazyRoute,
}

const AccountLayoutRouteWithChildren = AccountLayoutRoute._addFileChildren(
  AccountLayoutRouteChildren,
)

interface AccountRouteChildren {
  AccountLayoutRoute: typeof AccountLayoutRouteWithChildren
}

const AccountRouteChildren: AccountRouteChildren = {
  AccountLayoutRoute: AccountLayoutRouteWithChildren,
}

const AccountRouteWithChildren =
  AccountRoute._addFileChildren(AccountRouteChildren)

export interface FileRoutesByFullPath {
  '/': typeof appMainlayoutRouteWithChildren
  '/account': typeof appMainlayoutAccountRoute
  '/analytics': typeof appMainlayoutAnalyticsRoute
  '/dashboard': typeof appMainlayoutDashboardRoute
  '/edit-account': typeof appMainlayoutEditAccountRoute
  '/learn': typeof appMainlayoutLearnRoute
  '/mcqs': typeof appMainlayoutMcqsRoute
  '/selectplan': typeof appPricingSelectplanRoute
  '/account/login': typeof AccountLayoutLoginLazyRoute
  '/account/personalinfo': typeof AccountLayoutPersonalinfoLazyRoute
  '/account/register': typeof AccountLayoutRegisterLazyRoute
  '/account/resetpassword': typeof AccountLayoutResetpasswordLazyRoute
  '/account/verifyemail': typeof AccountLayoutVerifyemailLazyRoute
  '/t/mock': typeof appMainlayoutTMockRoute
}

export interface FileRoutesByTo {
  '/': typeof appMainlayoutRouteWithChildren
  '/account': typeof appMainlayoutAccountRoute
  '/analytics': typeof appMainlayoutAnalyticsRoute
  '/dashboard': typeof appMainlayoutDashboardRoute
  '/edit-account': typeof appMainlayoutEditAccountRoute
  '/learn': typeof appMainlayoutLearnRoute
  '/mcqs': typeof appMainlayoutMcqsRoute
  '/selectplan': typeof appPricingSelectplanRoute
  '/account/login': typeof AccountLayoutLoginLazyRoute
  '/account/personalinfo': typeof AccountLayoutPersonalinfoLazyRoute
  '/account/register': typeof AccountLayoutRegisterLazyRoute
  '/account/resetpassword': typeof AccountLayoutResetpasswordLazyRoute
  '/account/verifyemail': typeof AccountLayoutVerifyemailLazyRoute
  '/t/mock': typeof appMainlayoutTMockRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/(app)': typeof appRouteWithChildren
  '/(app)/_mainlayout': typeof appMainlayoutRouteWithChildren
  '/account': typeof AccountRouteWithChildren
  '/account/_layout': typeof AccountLayoutRouteWithChildren
  '/(app)/_mainlayout/account': typeof appMainlayoutAccountRoute
  '/(app)/_mainlayout/analytics': typeof appMainlayoutAnalyticsRoute
  '/(app)/_mainlayout/dashboard': typeof appMainlayoutDashboardRoute
  '/(app)/_mainlayout/edit-account': typeof appMainlayoutEditAccountRoute
  '/(app)/_mainlayout/learn': typeof appMainlayoutLearnRoute
  '/(app)/_mainlayout/mcqs': typeof appMainlayoutMcqsRoute
  '/(app)/_pricing/selectplan': typeof appPricingSelectplanRoute
  '/account/_layout/login': typeof AccountLayoutLoginLazyRoute
  '/account/_layout/personalinfo': typeof AccountLayoutPersonalinfoLazyRoute
  '/account/_layout/register': typeof AccountLayoutRegisterLazyRoute
  '/account/_layout/resetpassword': typeof AccountLayoutResetpasswordLazyRoute
  '/account/_layout/verifyemail': typeof AccountLayoutVerifyemailLazyRoute
  '/(app)/_mainlayout/t/mock': typeof appMainlayoutTMockRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/account'
    | '/analytics'
    | '/dashboard'
    | '/edit-account'
    | '/learn'
    | '/mcqs'
    | '/selectplan'
    | '/account/login'
    | '/account/personalinfo'
    | '/account/register'
    | '/account/resetpassword'
    | '/account/verifyemail'
    | '/t/mock'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/account'
    | '/analytics'
    | '/dashboard'
    | '/edit-account'
    | '/learn'
    | '/mcqs'
    | '/selectplan'
    | '/account/login'
    | '/account/personalinfo'
    | '/account/register'
    | '/account/resetpassword'
    | '/account/verifyemail'
    | '/t/mock'
  id:
    | '__root__'
    | '/'
    | '/(app)'
    | '/(app)/_mainlayout'
    | '/account'
    | '/account/_layout'
    | '/(app)/_mainlayout/account'
    | '/(app)/_mainlayout/analytics'
    | '/(app)/_mainlayout/dashboard'
    | '/(app)/_mainlayout/edit-account'
    | '/(app)/_mainlayout/learn'
    | '/(app)/_mainlayout/mcqs'
    | '/(app)/_pricing/selectplan'
    | '/account/_layout/login'
    | '/account/_layout/personalinfo'
    | '/account/_layout/register'
    | '/account/_layout/resetpassword'
    | '/account/_layout/verifyemail'
    | '/(app)/_mainlayout/t/mock'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  appRoute: typeof appRouteWithChildren
  AccountRoute: typeof AccountRouteWithChildren
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  appRoute: appRouteWithChildren,
  AccountRoute: AccountRouteWithChildren,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* prettier-ignore-end */

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/(app)",
        "/account"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/(app)": {
      "filePath": "(app)",
      "children": [
        "/(app)/_mainlayout",
        "/(app)/_pricing/selectplan"
      ]
    },
    "/(app)/_mainlayout": {
      "filePath": "(app)/_mainlayout.tsx",
      "parent": "/(app)",
      "children": [
        "/(app)/_mainlayout/account",
        "/(app)/_mainlayout/analytics",
        "/(app)/_mainlayout/dashboard",
        "/(app)/_mainlayout/edit-account",
        "/(app)/_mainlayout/learn",
        "/(app)/_mainlayout/mcqs",
        "/(app)/_mainlayout/t/mock"
      ]
    },
    "/account": {
      "filePath": "account",
      "children": [
        "/account/_layout"
      ]
    },
    "/account/_layout": {
      "filePath": "account/_layout.tsx",
      "parent": "/account",
      "children": [
        "/account/_layout/login",
        "/account/_layout/personalinfo",
        "/account/_layout/register",
        "/account/_layout/resetpassword",
        "/account/_layout/verifyemail"
      ]
    },
    "/(app)/_mainlayout/account": {
      "filePath": "(app)/_mainlayout/account.tsx",
      "parent": "/(app)/_mainlayout"
    },
    "/(app)/_mainlayout/analytics": {
      "filePath": "(app)/_mainlayout/analytics.tsx",
      "parent": "/(app)/_mainlayout"
    },
    "/(app)/_mainlayout/dashboard": {
      "filePath": "(app)/_mainlayout/dashboard.tsx",
      "parent": "/(app)/_mainlayout"
    },
    "/(app)/_mainlayout/edit-account": {
      "filePath": "(app)/_mainlayout/edit-account.tsx",
      "parent": "/(app)/_mainlayout"
    },
    "/(app)/_mainlayout/learn": {
      "filePath": "(app)/_mainlayout/learn.tsx",
      "parent": "/(app)/_mainlayout"
    },
    "/(app)/_mainlayout/mcqs": {
      "filePath": "(app)/_mainlayout/mcqs.tsx",
      "parent": "/(app)/_mainlayout"
    },
    "/(app)/_pricing/selectplan": {
      "filePath": "(app)/_pricing/selectplan.tsx",
      "parent": "/(app)"
    },
    "/account/_layout/login": {
      "filePath": "account/_layout.login.lazy.tsx",
      "parent": "/account/_layout"
    },
    "/account/_layout/personalinfo": {
      "filePath": "account/_layout.personalinfo.lazy.tsx",
      "parent": "/account/_layout"
    },
    "/account/_layout/register": {
      "filePath": "account/_layout.register.lazy.tsx",
      "parent": "/account/_layout"
    },
    "/account/_layout/resetpassword": {
      "filePath": "account/_layout.resetpassword.lazy.tsx",
      "parent": "/account/_layout"
    },
    "/account/_layout/verifyemail": {
      "filePath": "account/_layout.verifyemail.lazy.tsx",
      "parent": "/account/_layout"
    },
    "/(app)/_mainlayout/t/mock": {
      "filePath": "(app)/_mainlayout/t/mock.tsx",
      "parent": "/(app)/_mainlayout"
    }
  }
}
ROUTE_MANIFEST_END */
