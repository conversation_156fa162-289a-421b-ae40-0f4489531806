import { But<PERSON> } from "@/components/ui/button";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { createLazyFileRoute, useNavigate } from "@tanstack/react-router";
import { loginValidation, LoginValidation } from "@/features/auth/schema";
import { Checkbox } from "@/components/ui/checkbox";
import { ICONS } from "@/lib/assets/images";
import { useAuthStore } from "@/features/auth/store";
import { useShallow } from "zustand/react/shallow";
import {
	browserLocalPersistence,
	browserSessionPersistence,
	getAdditionalUserInfo,
	User,
} from "firebase/auth";
import { useCreateUser } from "@/lib/queries/auth.query";
import { formatErrorMsg } from "@/helpers/firebase";
import { useErrorToast } from "@/hooks/use-error-toast";
import { useState } from "react";
import auth from "@/lib/config/firebase";
import { UserCredential } from 'firebase/auth';

const Login = () => {
	const showError = useErrorToast();
	const navigate = useNavigate();
	const [rememberMe, setRememberMe] = useState(false);
	const {
		mutateAsync: createUserQuery,
		isError,
		error,
		isPending,
	} = useCreateUser();
	const { login, loginWithFacebook, loginWithGoogle, loading, sendFirebaseEmailVerification } = useAuthStore(
		useShallow((state) => ({
			login: state.login,
			user: state.user,
			loginWithGoogle: state.loginWithGoogle,
			loginWithFacebook: state.loginWithFacebook,
			loading: state.loading,
			sendFirebaseEmailVerification: state.sendFirebaseEmailVerification
		}))
	);
	const form = useForm<LoginValidation>({
		resolver: zodResolver(loginValidation),
		defaultValues: {
			email: "",
			password: "",
		},
	});

	const onSubmit = async (values: LoginValidation) => {
		const  result: UserCredential | Error = await login(values.email, values.password);
		if (result instanceof Error) {
			showError(formatErrorMsg(result.message));
		}else if(result?.user?.emailVerified === false) {
		    sendFirebaseEmailVerification(result?.user)
			navigate({ to: "/account/verifyemail" });
		}else {
			checkOnboarded(result.user);
		}
	};

	const registerWithGoogle = async () => {
		const result = await loginWithGoogle();
		if (result instanceof Error) {
			showError(formatErrorMsg(result.message));
		} else if (result) {
			const additionalInfo = getAdditionalUserInfo(result);
			if (additionalInfo?.isNewUser) {
				sendCreateUserToken();
			} else {
				checkOnboarded(result.user);
			}
		}
	};

	const checkOnboarded = async (user: User) => {
		const onboarded = (await user.getIdTokenResult()).claims["onboarded"];
		if (!onboarded) {
			navigate({ to: "/account/personalinfo" });
		} else {
			auth.setPersistence(
				rememberMe ? browserLocalPersistence : browserSessionPersistence
			);
		}
	};

	const sendCreateUserToken = async () => {
		const response = await createUserQuery();
		if (response?.data.success) {
			navigate({ to: "/account/personalinfo" });
		} else if (isError) {
			showError(error.message);
		}
	};

	return (
		<div className="h-full flex flex-col gap-y-4 p-4 sm:p-8 justify-center items-center bg-white rounded-lg">
			<h1>Welcome Back!</h1>
			<p className="text-gray-600 text-center max-w-[400px]">
				We’re excited to have your back. Log In now and access your account
			</p>
			<div className="w-full flex flex-col gap-y-2">
				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(onSubmit)}
						className="gap-y-4 flex flex-col"
					>
						<FormField
							control={form.control}
							name="email"
							render={({ field }) => (
								<FormItem className="space-y-0">
									<FormLabel className="text-gray-500 text-xs sm:text-sm">
										Email
									</FormLabel>
									<FormControl>
										<Input
											placeholder="<EMAIL>"
											type="email"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="password"
							render={({ field }) => (
								<FormItem className="space-y-0">
									<FormLabel className="text-gray-500 text-xs sm:text-sm">
										Password
									</FormLabel>
									<FormControl>
										<Input
											placeholder="Enter your password"
											type="password"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
						<div className="flex w-full justify-between">
							<div className="flex items-center gap-x-2">
								<Checkbox
									onCheckedChange={(checked) =>
										setRememberMe(checked === "indeterminate" ? false : checked)
									}
									id="rememberme"
									className="size-4"
								/>
								<label
									htmlFor="rememberme"
									className="text-xs sm:text-sm text-gray-500 peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
								>
									Remember Me
								</label>
							</div>
							<Button
								variant="link"
								className="text-xs sm:text-sm"
								type="button"
								onClick={() => navigate({ to: "/account/resetpassword" })}
							>
								Forgot password?
							</Button>
						</div>
						<Button
							disabled={loading || isPending}
							type="submit"
							className="w-full"
							loading={loading || isPending}
						>
							Log In
						</Button>
					</form>
				</Form>
				<div className="flex items-center">
					<hr className="flex-1 border-gray-300" />
					<p className="text-gray-400 px-2 text-sm">Or</p>
					<hr className="flex-1 border-gray-300" />
				</div>
				<div className="flex w-full gap-x-2">
					<Button
						variant="outline"
						className="flex-1 text-xs sm:text-sm"
						onClick={registerWithGoogle}
					>
						<img src={ICONS.google} alt="google" />
						Google
					</Button>
					<Button
						variant="outline"
						className="flex-1 text-xs sm:text-sm"
						onClick={loginWithFacebook}
					>
						<img src={ICONS.facebook} alt="facebook" />
						Facebook
					</Button>
				</div>
				<p className="text-center shadcn text-sm">
					Don't have an account?
					<span>
						<Button
							variant={"link"}
							className="px-0 pl-1"
							onClick={() => navigate({ to: "/account/register" })}
						>
							Sign up here
						</Button>
					</span>
				</p>
			</div>
		</div>
	);
};

export const Route = createLazyFileRoute("/account/_layout/login")({
	component: Login,
});
