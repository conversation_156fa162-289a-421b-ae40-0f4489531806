import { useState } from "react";
import {
	Bookmark,
	AlertTriangle,
	ChevronDown,
	ChevronUp,
	XCircle,
	CheckCircle,
} from "react-feather";
import { MCQ } from "@/features/mcqs/types";
import { Button } from "@/components/ui/button";
import { toggleMCQType } from "@/lib/utils";
import Latex from "react-latex-next";
import "katex/dist/katex.min.css";
import { Element } from "react-scroll";

type MCQCardProps = {
	mcq: MCQ;
	selectedAnswer: number | undefined;
	isCorrect?: boolean; // Changed to optional since we're not using it directly
	liveCheckEnabled: boolean;
	showResults: boolean;
	questionNumber?: number;
};

const MCQCard = ({
	mcq,
	selectedAnswer,
	// isCorrect removed from destructuring since it's not used
	liveCheckEnabled,
	showResults,
	questionNumber,
}: MCQCardProps) => {
	const [showExpertAnswer, setShowExpertAnswer] = useState(false);
	const [isBookmarked, setIsBookmarked] = useState(false);
	const hasAnswered = selectedAnswer !== undefined;
	const showFeedback = (liveCheckEnabled && hasAnswered) || showResults;

	const toggleBookmark = () => {
		setIsBookmarked((prev) => !prev);
	};

	console.log(mcq, "mcqs_______");

	// Determine if the given answer is actually correct (regardless of isCorrect prop)
	const isActuallyCorrect = selectedAnswer === mcq.correctAnswer;

	const difficultyStyles = {
		easy: "border-green-300 bg-green-100 text-green-800",
		medium: "border-yellow-300 bg-yellow-100 text-yellow-800",
		hard: "border-red-300 bg-red-100 text-red-800",
	};

	return (
		<Element name={mcq.id} className="p-6">
			<div className="flex items-center justify-between mb-4">
				<div className="flex items-center gap-2">
					<p className="text-gray-500 font-bold">
						QUESTION #{questionNumber !== undefined ? questionNumber : mcq.id}
					</p>
					{mcq.tag && (
						<span className="text-sm px-2 py-0.5 font-semibold border border-purple-300 bg-purple-100 text-accent rounded-full">
							{toggleMCQType(mcq.tag)}
						</span>
					)}
					{mcq.difficulty && (
						<span
							className={`text-sm px-2 py-0.5 font-semibold rounded-full border ${difficultyStyles[mcq.difficulty] || ""}`}
						>
							{mcq.difficulty}
						</span>
					)}
				</div>
				<div className="flex items-center gap-4 mx-4">
					<Button
						variant="icon"
						className="[&_svg]:size-5 px-0"
						onClick={toggleBookmark}
					>
						<Bookmark
							className={`w-5 h-5 text-gray-600 ${isBookmarked ? "fill-accent" : ""}`}
						/>
					</Button>
					<AlertTriangle className="w-5 h-5 text-gray-600" />
				</div>
			</div>

			<h3 className="mb-8 text-xl">
				<Latex>{mcq.question}</Latex>
			</h3>

			<div className="space-y-2">
				<p className="font-bold text-gray-400 mb-6">CHOOSE ANSWER</p>
				{mcq.options.map((option, index) => (
					<div
						key={mcq.id + option + index}
						className={`p-1 ${showFeedback && index === mcq.correctAnswer ? "bg-green-50 rounded" : ""}`}
					>
						<span
							className={`text-gray-400 ${showFeedback && index === mcq.correctAnswer ? "font-bold" : ""}`}
						>
							({String.fromCharCode(65 + index)})
						</span>
						<span
							className={`text-base font-semibold ml-2 ${
								showFeedback && index === mcq.correctAnswer
									? "text-green-600"
									: showFeedback &&
										  index === selectedAnswer &&
										  !isActuallyCorrect
										? "text-red-600"
										: ""
							}`}
						>
							<Latex>{option}</Latex>
						</span>
						{showFeedback && index === mcq.correctAnswer && (
							<span className="ml-2 text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded">
								Correct Answer
							</span>
						)}
					</div>
				))}
			</div>

			{showFeedback && (
				<div className="mt-4">
					<div className="flex flex-col item-start sm:flex-row sm:items-center sm:justify-between gap-4">
						<div
							className={`py-2 px-4 rounded-full max-w-[90%] ${
								isActuallyCorrect
									? "bg-green-100 text-green-700"
									: "bg-red-100 text-red-700"
							} flex items-center gap-2`}
						>
							<span className="text-sm flex items-center gap-2">
								{isActuallyCorrect ? (
									<CheckCircle className="w-4 h-4" />
								) : (
									<XCircle className="w-4 h-4" />
								)}
								{isActuallyCorrect
									? "You have chosen the correct answer."
									: hasAnswered
										? "You have chosen the wrong answer."
										: "You have not answered this question."}
							</span>
						</div>
						<Button
							variant="link"
							onClick={() => setShowExpertAnswer(!showExpertAnswer)}
							className="justify-start text-gray-600 flex items-center gap-1 text-sm"
						>
							{showExpertAnswer ? "Hide" : "View"} expert answer
							{showExpertAnswer ? (
								<ChevronUp size={16} />
							) : (
								<ChevronDown size={16} />
							)}
						</Button>
					</div>

					{showExpertAnswer && (
						<div className="mt-2 p-4 bg-gray-50 rounded-md">
							<p className="text-sm">
								<Latex>{String(mcq.description)}</Latex>
							</p>
							{!isActuallyCorrect && (
								<div className="mt-2 pt-2 border-t border-gray-200">
									<p className="text-sm font-medium text-green-600">
										Correct answer:{" "}
										{String.fromCharCode(65 + mcq.correctAnswer)} -{" "}
										<Latex>
											{String(mcq.options[mcq.correctAnswer] ?? "")}
										</Latex>
									</p>
								</div>
							)}
						</div>
					)}
				</div>
			)}
		</Element>
	);
};

export default MCQCard;
