import { APIResponse } from "@/lib/api/types";

export type Resource = {
	_id: string;
	subject: string[];
	type: string;
	entryTest: string[];
	title: string;
	description: string;
	chapter: string[];
	topic: string;
	url: string;
	id: string;
};

export type FilterParams = {
	entryTests?: string[];
	chapters?: string[];
	topics?: string[];
	language?: string[];
	categories?: string[];
};

export type FilterOption = {
	label: string;
	value: string;
};

export type FilterData = {
	entryTest: string[];
	chapters: string[];
	topic: string[];
	langs: string[];
	categories: string[];
};

export type GetResourcesRes = APIResponse<Resource[]>;
export type GetFiltersRes = APIResponse<FilterData>;
